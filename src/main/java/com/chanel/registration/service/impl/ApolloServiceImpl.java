package com.chanel.registration.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.chanel.registration.common.constants.Constants;
import com.chanel.registration.common.exception.BaseException;
import com.chanel.registration.common.utils.*;
import com.chanel.registration.config.apollo.ApolloApi;
import com.chanel.registration.config.apollo.ApolloProperties;
import com.chanel.registration.config.apollo.ApolloResource;
import com.chanel.registration.config.apollo.PioneerHeaderConfig;
import com.chanel.registration.dto.Apollo2TokenResBodyDTO;
import com.chanel.registration.dto.ApolloResponseXml;
import com.chanel.registration.dto.NotificationCenterResponseDTO;
import com.chanel.registration.dto.NotificationCenterSMSRequestDTO;
import com.chanel.registration.dto.pionner.*;
import com.chanel.registration.entity.Address;
import com.chanel.registration.entity.ContactNumber;
import com.chanel.registration.entity.Customer;
import com.chanel.registration.entity.OnlineUser;
import com.chanel.registration.enums.ErrorCodeEnum;
import com.chanel.registration.service.IApolloService;
import com.chanel.registration.vo.CustomerVo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 阿波罗服务impl
 *
 * <AUTHOR>
 * @date 2022/04/07 21:16
 **/
@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("all")
public class ApolloServiceImpl implements IApolloService {

    public static final String TITLE_PREFIX = "Do not want to disclose";
    private final ApolloProperties apolloProperties;
    private final AesEncryptUtils aesEncryptUtils;
    private final PioneerHeaderConfig pioneerHeaderConfig;

    @Override
    public List<CustomerVo> findPcnCustomer(Map<String, Object> queryParam) {
        Map<String, List<String>> headers = Maps.newHashMap();
        headers.put("sys", Lists.newArrayList("LionIpad"));
        Map result = apolloPost(apolloProperties.getApolloCx().getLionRecords(), headers, queryParam);
        if (CollectionUtils.isEmpty(result)) {
            log.warn("ApolloServiceImpl::findCustomer ==> apollo server is not responding");
            throw new BaseException(ErrorCodeEnum.HTTP_CALL_ERROR.getCode(), "apollo server is not responding");
        }
        Boolean isSuccess = (Boolean) result.get("success");
        if (!Boolean.TRUE.equals(isSuccess)) {
            log.warn("ApolloServiceImpl::findCustomer ==> call apollo to failure, cause: [{}]", (String) result.get("message"));
            throw new BaseException(ErrorCodeEnum.HTTP_CALL_ERROR.getCode(), (String) result.get("message"));
        }
        Object objects = result.get("object");
        if (Objects.isNull(objects)) {
            return Lists.newArrayList();
        }
        JSON.defaultTimeZone = TimeZone.getTimeZone("Asia/Shanghai");
        String jsonString = JSON.toJSONString(objects);
        List<CustomerVo> customerVoList = JSON.parseArray(jsonString, CustomerVo.class);
        String toJSONString = JSON.toJSONString(customerVoList);
        log.debug("apolloQuery ==> customerVoList toJSONString: {}", toJSONString);
        if (CollUtil.isEmpty(customerVoList)) {
            customerVoList = Lists.newArrayList();
        }
        customerVoList.forEach(customerVo -> {
            String title = customerVo.getTitle();
            if (StringUtils.isNotEmpty(title) && title.startsWith(TITLE_PREFIX)) {
                customerVo.setTitle("");
            }
        });
        return customerVoList;
    }

    /**
     * @param queryParam
     * @return
     */
    @Override
    public List<CustomerVo> findPgnCustomer(Map<String, Object> queryParam) {
        try {
            List<CustomerVo> customerVoList = Lists.newArrayList();
            OnlineUser onlineUser = (OnlineUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            // 构建请求URL和参数
            String apiUrl = apolloProperties.getApolloCx().getPioneerRecores().getUrl() + "/int/global/lookupapi/api/lookupapi/customers/search";
            Map<String, String> headers = Maps.newHashMap();
            headers.putAll(pioneerHeaderConfig.toHeaderMap());

            // 发送请求
            long start = System.currentTimeMillis();
            HttpRequest request = HttpRequest.get(apiUrl)
                    .form(queryParam)
                    .addHeaders(headers)
                    .timeout(30000);

            String body = request.execute().body();
            log.info("PGN API request: [{}] time consuming {} ms", apiUrl, System.currentTimeMillis() - start);
            log.debug("request PGN API url={}, params={}, response={}",
                    apiUrl, JSONUtil.toJsonStr(queryParam),
                    StringUtils.isNotBlank(body) ? PlaceholderUtil.replaceBlank(body) : body);

            // 解析响应
            if (StringUtils.isEmpty(body)) {
                log.warn("PGN API response is empty");
                return Lists.newArrayList();
            }

            // 将响应转换为CustomerVo列表
            PioneerResponseDTO response = JSON.parseObject(body, PioneerResponseDTO.class);


            // pioneer搜索结果转换为CustomerVo

            // 按照转换规则，实现adaptPioneerResponse方法
            adaptPioneerResponse(response, customerVoList);

            if (CollUtil.isEmpty(customerVoList)) {
                return Lists.newArrayList();
            }

            // 处理标题信息
            customerVoList.forEach(customerVo -> {
                String title = customerVo.getTitle();
                if (StringUtils.isNotEmpty(title) && title.startsWith(TITLE_PREFIX)) {
                    customerVo.setTitle("");
                }
            });

            return customerVoList;
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error calling PGN API", e);
            throw new BaseException(ErrorCodeEnum.HTTP_CALL_ERROR.getCode(), "Error calling PGN API: " + e.getMessage());
        }
    }

    @Override
    public String generateVipCode(Object data) {
        Map<String, List<String>> headers = Maps.newHashMap();
        headers.put("sys", Lists.newArrayList("POS"));
        headers.put("posid", Lists.newArrayList("FCHNPOS"));
        ApolloResponseXml result;
        if (apolloProperties.getApolloCx().getPosRecords().isEnabled()) {
            result = parseResult(doXmlRequestApollo2(apolloProperties.getApolloCx().getPosRecords().getUrl(), headers, data));
        } else {
            result = parseResult(doXmlRequest(apolloProperties.getApolloCx().getPosRecords().getOldUrl(), headers, data));
        }
        if (Objects.isNull(result)) {
            log.warn("ApolloServiceImpl::generateVipCode ==> apollo server is not responding");
            throw new BaseException(ErrorCodeEnum.GENERATE_VIP_CODE_ERROR);
        }

        if (!Boolean.TRUE.equals(result.getSuccess())) {
            log.warn("ApolloServiceImpl::generateVipCode ==> call apollo to failure, cause: [{}]", result.getErrorMessage());
            String errorMessage = result.getErrorMessage();
            if (StringUtils.isNotEmpty(errorMessage) && errorMessage.startsWith("Duplicate VIP Code")) {
                throw new BaseException(ErrorCodeEnum.DUPLICATE_VIP_CODE);
            }
            throw new BaseException(ErrorCodeEnum.GENERATE_VIP_CODE_ERROR.getCode(), errorMessage);
        }
        return result.getErrorMessage();
    }

    private ApolloResponseXml parseResult(String body) {
        try {
            Document document = DocumentHelper.parseText(body);
            String responseXml = document.getRootElement()
                    .element("Body")
                    .element("uf_newvip_v3Response")
                    .element("uf_newvip_v3Result")
                    .element("stru_header")
                    .asXML();
            return XmlUtil.toBean(responseXml, ApolloResponseXml.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.warn("failed to parse apollo xml response", e.getMessage());
            return null;
        }

    }

    @Override
    public Boolean syncToLion(Customer customer) {
        Map<String, List<String>> headers = Maps.newHashMap();
        headers.put("sys", Lists.newArrayList("LIONiPad"));
        headers.put("div", Lists.newArrayList("FSN"));
        headers.put("typ", Lists.newArrayList("Customer"));
        Map result = apolloPost(apolloProperties.getApolloCx().getCreateCustomer(), headers, customer);
        if (CollectionUtils.isEmpty(result)) {
            log.warn("ApolloServiceImpl::syncToLion ==> apollo server is not responding");
            throw new BaseException(ErrorCodeEnum.HTTP_CALL_ERROR.getCode(), "apollo server is not responding");
        }

        String responseId = (String) result.get("id");
        if (!StringUtils.isEmpty(responseId)) {
            customer.setResponseId(responseId);
        }
        Boolean isSuccess = (Boolean) result.get("success");
        if (!Boolean.TRUE.equals(isSuccess)) {
            log.warn("sync to lion is failed, reason is {}", result.get("message"));
        }
        return isSuccess;
    }

    @Override
    public Boolean sendSms(String mobile, String verificationCode) {
        try {
            long start = System.currentTimeMillis();
            NotificationCenterSMSRequestDTO smsRequestDTO = new NotificationCenterSMSRequestDTO();
            HashMap<String, String> param = Maps.newHashMap();
            param.put("verificationCode", verificationCode);
            smsRequestDTO.setParam(param);
            NotificationCenterSMSRequestDTO.Receiver receiver = new NotificationCenterSMSRequestDTO.Receiver();
            receiver.setToList(Lists.newArrayList(mobile));
            smsRequestDTO.setReceiver(receiver);
            String apiUrl = apolloProperties.getNotificationCenter().getHost() + apolloProperties.getNotificationCenter().getSendSms().getUrl();

            HashMap<String, String> encryptRequest = Maps.newHashMap();
            encryptRequest.put("data", RSACoderUtil.publicEncrypt(JSON.toJSONString(smsRequestDTO, SerializerFeature.WriteNullListAsEmpty), apolloProperties.getNotificationCenter().getPublicKey()));

            HttpRequest request = HttpRequest.post(apiUrl)
                    .header("Authorization", String.format("Bearer %s", getOauth2Token(apolloProperties.getNotificationCenter())))
                    .header("Accept", "application/json")
                    .contentType("application/json")
                    .body(JSON.toJSONString(encryptRequest))
                    .timeout(20000);
            String body = request.execute().body();
            log.info("apollo request: [{}] time consuming {} ms", apiUrl, System.currentTimeMillis() - start);
            log.debug("request apollo url= {}, request= {}, response= {}", apiUrl, JSONUtil.toJsonStr(smsRequestDTO), StringUtils.isNotBlank(body) ? PlaceholderUtil.replaceBlank(body) : body);

            NotificationCenterResponseDTO responseDTO = JSONUtil.toBean(body, NotificationCenterResponseDTO.class);
            return responseDTO.getSuccess();
        } catch (Exception e) {
            log.error("call notification center send sms is failed, {}", e.getMessage());
            return false;
        }
    }


    private Map apolloPost(ApolloApi api, Map<String, List<String>> headers, Object data) {
        String body;
        if (api.isEnabled()) {
            body = doRequestApollo2(api.getUrl(), headers, data);
        } else {
            body = doRequest(api.getOldUrl(), headers, data);
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Map<String, Object> result = objectMapper.readValue(body, Map.class);
            return result;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return Maps.newHashMap();
        }
    }

    private String doRequest(String apiUrl, Map<String, List<String>> headers, Object data) {
        log.info("apollo interface [{}] request satrt", apolloProperties.getUrl() + apiUrl);
        long start = System.currentTimeMillis();
        HttpRequest request = HttpRequest.post(apolloProperties.getUrl() + apiUrl)
                .header(headers)
                .header("Sub-key", apolloProperties.getSubKey())
                .header("Accept", "application/json")
                .basicAuth(apolloProperties.getUsername(), apolloProperties.getPassword())
                .contentType("application/json")
                .body(JSON.toJSONString(data, SerializerFeature.WriteNullListAsEmpty))
                .timeout(20000);
        log.debug("request detail info, {}", request.toString());
        String body = request.execute().body();
        log.info("apollo request: [{}] time consuming {} ms", apiUrl, System.currentTimeMillis() - start);
        log.debug("request apollo url= {}, request= {}, response= {}", apiUrl, JSONUtil.toJsonStr(data), StringUtils.isNotBlank(body) ? PlaceholderUtil.replaceBlank(body) : body);
        return body;
    }

    private String doXmlRequest(String apiUrl, Map<String, List<String>> headers, Object data) {
        log.info("apollo interface [{}] request satrt", apolloProperties.getUrl() + apiUrl);
        long start = System.currentTimeMillis();
        HttpRequest request = HttpRequest.post(apolloProperties.getUrl() + apiUrl)
                .header(headers)
                .header("Sub-key", apolloProperties.getSubKey())
                .contentType("text/xml; charset=utf-8")
                .basicAuth(apolloProperties.getUsername(), apolloProperties.getPassword())
                .body(data.toString())
                .timeout(40000);
        log.debug("request detail info, {}", request.toString());
        String body = request.execute().body();
        log.info("apollo request: [{}] time consuming {} ms", apiUrl, System.currentTimeMillis() - start);
        log.debug("request apollo url= {}, request= {}, response= {}", apiUrl, XmlUtil.compressXmlStr((String) data), body);
        return body;
    }


    /**
     * Apollo2 Get Oauth2Token
     *
     * @return
     */
    private String getOauth2Token(ApolloResource apolloResource) {
        String accessToken = (String) RedisUtil.get(Constants.CACHE_APOLLO_OAUTH2_TOKEN + apolloResource.getCacheKey());
        if (StringUtils.isNotEmpty(accessToken)) {
            return accessToken;
        }
        return getNewOauth2Token(apolloResource);
    }

    /**
     * Apollo2 Get New Oauth2Token
     *
     * @return
     */
    private String getNewOauth2Token(ApolloResource apolloResource) {
        long start = System.currentTimeMillis();
        Map<String, Object> requestBody = Maps.newHashMap();
        requestBody.put("grant_type", "client_credentials");
        requestBody.put("scope", "openid");
        requestBody.put("resource", apolloResource.getResource());
        HttpRequest request = HttpRequest.post(apolloProperties.getAccessTokenUrl())
                .form(requestBody)
                .header("client_id", apolloProperties.getClientId())
                .header("client_secret", apolloProperties.getClientSecret())
                .contentType("application/x-www-form-urlencoded;charset=UTF-8")
                .basicAuth(apolloProperties.getClientId(), apolloProperties.getClientSecret())
                .timeout(20000);
        log.debug("request detail info, {}", request.toString());
        String body = request.execute().body();
        log.info("apollo request: [{}] time consuming {} ms", apolloProperties.getAccessTokenUrl(), System.currentTimeMillis() - start);
        log.debug("request apollo url= {}, resource= {}, response= {}", apolloProperties.getAccessTokenUrl(), apolloResource.getResource(), body);
        Apollo2TokenResBodyDTO apollo2TokenResBodyModel = JSON.parseObject(body, Apollo2TokenResBodyDTO.class);
        RedisUtil.set(Constants.CACHE_APOLLO_OAUTH2_TOKEN + apolloResource.getCacheKey(), apollo2TokenResBodyModel.getAccessToken(), apollo2TokenResBodyModel.getExpiresIn() - 100, TimeUnit.SECONDS);
        return apollo2TokenResBodyModel.getAccessToken();
    }

    private String doRequestApollo2(String apiUrl, Map<String, List<String>> headers, Object data) {
        apiUrl = apolloProperties.getApolloCx().getHost() + apiUrl;
        log.info("apollo interface [{}] request satrt", apiUrl);
        long start = System.currentTimeMillis();
        HttpRequest request = HttpRequest.post(apiUrl)
                .header(headers)
                .header("Authorization", String.format("Bearer %s", getOauth2Token(apolloProperties.getApolloCx())))
                .header("Accept", "application/json")
                .contentType("application/json")
                .body(JSON.toJSONString(data, SerializerFeature.WriteNullListAsEmpty))
                .timeout(20000);
        log.debug("request detail info, {}", request.toString());
        String body = request.execute().body();
        log.info("apollo request: [{}] time consuming {} ms", apiUrl, System.currentTimeMillis() - start);
        log.debug("request apollo url= {}, request= {}, response= {}", apiUrl, JSON.toJSONString(data, SerializerFeature.WriteNullListAsEmpty), StringUtils.isNotBlank(body) ? PlaceholderUtil.replaceBlank(body) : body);
        return body;
    }

    private String doXmlRequestApollo2(String apiUrl, Map<String, List<String>> headers, Object data) {
        apiUrl = apolloProperties.getApolloCx().getHost() + apiUrl;
        log.info("apollo interface [{}] request satrt", apiUrl);
        long start = System.currentTimeMillis();
        HttpRequest request = HttpRequest.post(apiUrl)
                .header(headers)
                .header("Authorization", String.format("Bearer %s", getOauth2Token(apolloProperties.getApolloCx())))
                .contentType("text/xml; charset=utf-8")
                .body(data.toString())
                .timeout(40000);
        log.debug("request detail info, {}", request.toString());
        String body = request.execute().body();
        log.info("apollo request: [{}] time consuming {} ms", apiUrl, System.currentTimeMillis() - start);
        log.debug("request apollo url= {}, request= {}, response= {}", apiUrl, XmlUtil.compressXmlStr((String) data), body);
        return body;
    }

    /**
     * 根据转换规则将Pioneer搜索结果适配为本地CustomerVo
     *
     * @param response       Pioneer响应数据
     * @param customerVoList 输出的CustomerVo列表
     */
    private void adaptPioneerResponse(PioneerResponseDTO response, List<CustomerVo> customerVoList) {
        if (response == null || CollectionUtils.isEmpty(response.getResults())) {
            return;
        }

        for (CustomerProfile profile : response.getResults()) {
            CustomerVo customerVo = new CustomerVo();

            // 1. Global ID映射
            customerVo.setOneid(profile.getCustomerGlobalId());

            // 2. 姓名映射 - 按照优先级规则
            adaptName(profile, customerVo);

            // 3. 手机号映射 - 优先CN地区Mobile类型
            adaptPhone(profile, customerVo);

            // 4. 邮箱映射 - 优先CN地区
            adaptEmail(profile, customerVo);

            // 5. 地址映射 - 优先Communication/Home类型，CN国家
            adaptAddress(profile, customerVo);

            // 6. 其他基本信息映射
            adaptBasicInfo(profile, customerVo);

            customerVoList.add(customerVo);
        }
    }

    /**
     * 姓名适配 - 按照优先级规则
     * 1st priority: 如果written name (last & first) 是中文，使用written name
     * 2nd priority: written name不是中文，latinized name不为空，使用latinized name
     * 如果都不满足，则两个字段都不填充
     */
    private void adaptName(CustomerProfile profile, CustomerVo customerVo) {
        if (profile.getBasicInfo() == null) {
            return;
        }

        BasicInfo basicInfo = profile.getBasicInfo();
        String lastName = null;
        String firstName = null;

        // 1st priority: 检查written name是否为中文
        if (basicInfo.getWrittenName() != null) {
            WrittenName writtenName = basicInfo.getWrittenName();
            if (StringUtils.isNotEmpty(writtenName.getLastName()) &&
                    StringUtils.isNotEmpty(writtenName.getFirstName()) &&
                    isChinese(writtenName.getLastName()) && isChinese(writtenName.getFirstName())) {
                lastName = writtenName.getLastName();
                firstName = writtenName.getFirstName();
            }
        }

        // 2nd priority: 如果written name不是中文，检查latinized name
        if (lastName == null && firstName == null && basicInfo.getLatinizedName() != null) {
            LatinizedName latinizedName = basicInfo.getLatinizedName();
            if (latinizedName.getAutomatic() != null) {
                Name automatic = latinizedName.getAutomatic();
                if (StringUtils.isNotEmpty(automatic.getLastName()) &&
                        StringUtils.isNotEmpty(automatic.getFirstName())) {
                    lastName = automatic.getLastName();
                    firstName = automatic.getFirstName();
                }
            }
        }

        // 设置姓名字段
        if (lastName != null && firstName != null) {
            customerVo.setLocalSurname(lastName);
            customerVo.setLocalGivenName(firstName);
            customerVo.setFullName(lastName + firstName); // 中文：姓+名，英文：名+姓
        }
    }

    /**
     * 判断字符串是否包含中文字符
     */
    private boolean isChinese(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches(".*[\\u4e00-\\u9fa5].*");
    }

    /**
     * 手机号适配 - 优先CN地区Mobile类型
     * 最多显示2个手机号：类型优先级 Mobile > Assistant > Home > Other
     * 国家优先级：CN > 其他（按首字母升序）
     */
    private void adaptPhone(CustomerProfile profile, CustomerVo customerVo) {
        if (profile.getContactInfo() == null || profile.getContactInfo().getPhones() == null) {
            return;
        }

        Phone[] phones = profile.getContactInfo().getPhones();
        List<Phone> validPhones = Lists.newArrayList();

        // 过滤有效的手机号
        for (Phone phone : phones) {
            if (phone != null && StringUtils.isNotEmpty(phone.getNumber())) {
                validPhones.add(phone);
            }
        }

        if (validPhones.isEmpty()) {
            return;
        }

        // 按照优先级排序：类型优先级 + 国家优先级
        validPhones.sort((p1, p2) -> {
            // 类型优先级比较
            int typeCompare = getPhoneTypePriority(p1.getType()) - getPhoneTypePriority(p2.getType());
            if (typeCompare != 0) {
                return typeCompare;
            }

            // 国家优先级比较：CN优先，其他按字母排序
            String country1 = StringUtils.defaultString(p1.getMccCountry(), "");
            String country2 = StringUtils.defaultString(p2.getMccCountry(), "");

            if ("CN".equals(country1) && !"CN".equals(country2)) {
                return -1;
            } else if (!"CN".equals(country1) && "CN".equals(country2)) {
                return 1;
            } else {
                return country1.compareTo(country2);
            }
        });

        // 设置主要手机号（第一个）
        Phone primaryPhone = validPhones.get(0);
        customerVo.setMobile(primaryPhone.getNumber());

        // 构建联系电话列表（最多2个）
        List<ContactNumber> contactNumbers = Lists.newArrayList();
        for (int i = 0; i < Math.min(validPhones.size(), 2); i++) {
            Phone phone = validPhones.get(i);
            ContactNumber contactNumber = ContactNumber.builder()
                    .phoneType(phone.getType())
                    .countryCode(phone.getMccCountry())
                    .phoneNumber(phone.getNumber())
                    .build();
            contactNumbers.add(contactNumber);
        }
        customerVo.setContactNumbers(contactNumbers);
    }

    /**
     * 获取手机类型优先级
     */
    private int getPhoneTypePriority(String type) {
        if (StringUtils.isEmpty(type)) {
            return 999;
        }
        switch (type.toLowerCase()) {
            case "mobile":
                return 1;
            case "assistant":
                return 2;
            case "home":
                return 3;
            case "office":
                return 4;
            case "other":
                return 5;
            default:
                return 999;
        }
    }

    /**
     * 邮箱适配 - 优先CN地区，最多显示1个邮箱
     */
    private void adaptEmail(CustomerProfile profile, CustomerVo customerVo) {
        if (profile.getContactInfo() == null || profile.getContactInfo().getEmails() == null) {
            return;
        }

        Email[] emails = profile.getContactInfo().getEmails();
        List<Email> validEmails = Lists.newArrayList();

        // 过滤有效的邮箱
        for (Email email : emails) {
            if (email != null && StringUtils.isNotEmpty(email.getValue()) &&
                    isValidEmail(email.getValue())) {
                validEmails.add(email);
            }
        }

        if (validEmails.isEmpty()) {
            return;
        }

        // 按照地区优先级排序：CN优先，其他按字母排序
        validEmails.sort((e1, e2) -> {
            String region1 = StringUtils.defaultString(e1.getRegionId(), "");
            String region2 = StringUtils.defaultString(e2.getRegionId(), "");

            if ("CN".equals(region1) && !"CN".equals(region2)) {
                return -1;
            } else if (!"CN".equals(region1) && "CN".equals(region2)) {
                return 1;
            } else {
                return region1.compareTo(region2);
            }
        });

        // 设置第一个邮箱
        customerVo.setEmail(validEmails.get(0).getValue());
    }

    /**
     * 简单的邮箱格式验证
     */
    private boolean isValidEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return false;
        }
        return email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }

    /**
     * 地址适配 - 优先Communication/Home类型，CN国家，最多显示2个地址
     */
    private void adaptAddress(CustomerProfile profile, CustomerVo customerVo) {
        if (profile.getContactInfo() == null) {
            return;
        }

        List<com.chanel.registration.dto.pionner.Address> allAddresses = Lists.newArrayList();

        // 收集所有地址
        if (profile.getContactInfo().getAddressesTypeCountry() != null) {
            allAddresses.addAll(Arrays.asList(profile.getContactInfo().getAddressesTypeCountry()));
        }
        if (profile.getContactInfo().getAddressesTypeRegion() != null) {
            allAddresses.addAll(Arrays.asList(profile.getContactInfo().getAddressesTypeRegion()));
        }
        if (profile.getContactInfo().getAddressesTypeSubtypeCountry() != null) {
            allAddresses.addAll(Arrays.asList(profile.getContactInfo().getAddressesTypeSubtypeCountry()));
        }

        List<com.chanel.registration.dto.pionner.Address> validAddresses = Lists.newArrayList();

        // 过滤有效的地址
        for (com.chanel.registration.dto.pionner.Address address : allAddresses) {
            if (address != null && address.getValue() != null &&
                    StringUtils.isNotEmpty(address.getValue().getCountry())) {
                validAddresses.add(address);
            }
        }

        if (validAddresses.isEmpty()) {
            return;
        }

        // 按照优先级排序：类型优先级 + 子类型优先级 + 国家优先级
        validAddresses.sort((a1, a2) -> {
            // 类型优先级比较
            int typeCompare = getAddressTypePriority(a1.getType()) - getAddressTypePriority(a2.getType());
            if (typeCompare != 0) {
                return typeCompare;
            }

            // 子类型优先级比较
            int subTypeCompare = getAddressSubTypePriority(a1.getSubType()) - getAddressSubTypePriority(a2.getSubType());
            if (subTypeCompare != 0) {
                return subTypeCompare;
            }

            // 国家优先级比较：CN优先，其他按字母排序
            String country1 = StringUtils.defaultString(a1.getValue().getCountry(), "");
            String country2 = StringUtils.defaultString(a2.getValue().getCountry(), "");

            if ("CN".equals(country1) && !"CN".equals(country2)) {
                return -1;
            } else if (!"CN".equals(country1) && "CN".equals(country2)) {
                return 1;
            } else {
                return country1.compareTo(country2);
            }
        });

        // 构建地址列表（最多2个）
        List<Address> addresses = Lists.newArrayList();
        for (int i = 0; i < Math.min(validAddresses.size(), 2); i++) {
            com.chanel.registration.dto.pionner.Address pioneerAddress = validAddresses.get(i);
            AddressValue addressValue = pioneerAddress.getValue();

            Address address = Address.builder()
                    .addressType(pioneerAddress.getType())
                    .country(addressValue.getCountry())
                    .province(addressValue.getStateProvince())
                    .city(addressValue.getCity())
                    .district(StringUtils.defaultString(addressValue.getLine1(), ""))
                    .addressLine(buildAddressLine(addressValue))
                    .zipCode(addressValue.getZipCode())
                    .build();
            addresses.add(address);
        }
        customerVo.setAddresses(addresses);
    }

    /**
     * 获取地址类型优先级
     */
    private int getAddressTypePriority(String type) {
        if (StringUtils.isEmpty(type)) {
            return 999;
        }
        switch (type.toLowerCase()) {
            case "communication":
                return 1;
            case "billing":
                return 2;
            case "shipping":
                return 3;
            default:
                return 999;
        }
    }

    /**
     * 获取地址子类型优先级
     */
    private int getAddressSubTypePriority(String subType) {
        if (StringUtils.isEmpty(subType)) {
            return 999;
        }
        switch (subType.toLowerCase()) {
            case "home":
                return 1;
            case "work":
                return 2;
            case "vacation":
                return 3;
            default:
                return 999;
        }
    }

    /**
     * 构建详细地址行
     */
    private String buildAddressLine(AddressValue addressValue) {
        List<String> addressParts = Lists.newArrayList();

        if (StringUtils.isNotEmpty(addressValue.getLine1())) {
            addressParts.add(addressValue.getLine1());
        }
        if (StringUtils.isNotEmpty(addressValue.getLine2())) {
            addressParts.add(addressValue.getLine2());
        }
        if (StringUtils.isNotEmpty(addressValue.getLine3())) {
            addressParts.add(addressValue.getLine3());
        }
        if (StringUtils.isNotEmpty(addressValue.getLine4())) {
            addressParts.add(addressValue.getLine4());
        }

        return String.join(" ", addressParts);
    }

    /**
     * 基本信息适配
     */
    private void adaptBasicInfo(CustomerProfile profile, CustomerVo customerVo) {
        if (profile.getBasicInfo() == null) {
            return;
        }

        BasicInfo basicInfo = profile.getBasicInfo();

        // 称谓映射 - 只接受标准格式
        if (StringUtils.isNotEmpty(basicInfo.getCivility())) {
            String civility = basicInfo.getCivility();
            if ("Mr.".equals(civility) || "Ms.".equals(civility) || "PreferNotToSay".equals(civility)) {
                customerVo.setTitle(civility);
            }
        }

        // 国籍映射
        if (StringUtils.isNotEmpty(basicInfo.getNationality())) {
            customerVo.setNationality(basicInfo.getNationality());
        }

        // 居住国家映射
        if (basicInfo.getCustomerCountry() != null &&
                StringUtils.isNotEmpty(basicInfo.getCustomerCountry().getCountryOfResidence())) {
            customerVo.setLocationOfResidence(basicInfo.getCustomerCountry().getCountryOfResidence());
        }

        // 生日映射 - 只有年龄在14-120之间才填充
        if (basicInfo.getBirthDate() != null) {
            BirthDate birthDate = basicInfo.getBirthDate();
            if (birthDate.getYear() != null && birthDate.getMonth() != null && birthDate.getDay() != null) {
                try {
                    LocalDate birthday = LocalDate.of(birthDate.getYear(), birthDate.getMonth(), birthDate.getDay());
                    int age = LocalDate.now().getYear() - birthday.getYear();
                    if (age > 14 && age < 120) {
                        customerVo.setBirthday(birthday);
                    }
                } catch (Exception e) {
                    log.warn("Invalid birth date: year={}, month={}, day={}",
                            birthDate.getYear(), birthDate.getMonth(), birthDate.getDay());
                }
            }
        }

        // 设置注册状态 - 根据GlobalClientStatus判断
        if (basicInfo.getGlobalClientStatus() != null) {
            GlobalClientStatus status = basicInfo.getGlobalClientStatus();
            customerVo.setIsRegisterFSN(StringUtils.isNotEmpty(status.getFSHStatus()));
            customerVo.setIsRegisterWFJ(StringUtils.isNotEmpty(status.getWFJStatus()));
        }

        // 设置地区信息 - 从markets中获取
        if (basicInfo.getMarkets() != null && basicInfo.getMarkets().length > 0) {
            // 优先选择CN地区的market
            Market selectedMarket = null;
            for (Market market : basicInfo.getMarkets()) {
                if ("CN".equals(market.getRegionId())) {
                    selectedMarket = market;
                    break;
                }
            }
            // 如果没有CN地区，选择第一个
            if (selectedMarket == null) {
                selectedMarket = basicInfo.getMarkets()[0];
            }
            // 这里可以根据需要设置其他字段，目前CustomerVo中没有对应的region字段
        }
    }
}
