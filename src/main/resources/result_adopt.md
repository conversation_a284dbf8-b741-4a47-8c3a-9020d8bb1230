# Local Adaptation

When downstream lookup to <PERSON>, below is the
local logic to be adapted:

| **Data** **Type** | **Data** **Attributes** | **Pioneer Standard** | **Pioneer Sample**                                                                                                                                                                              | **Local Adaptation**                                                                                                                                                                                                                                                                                         | **LION Sample**                                                                                                                                                                                                                                                                                                                                                       |
| ----------------- | ----------------------- | -------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| CX & TX           | Timestamp               |                      | GMT 格林威治时间                                                                                                                                                                                      | 2025-04-08T02:00:00Z                                                                                                                                                                                                                                                                                         | CST (GMT + 8h) 北京时间                                                                                                                                                                                                                                                                                                                                                   |
| TX                | Currency                |                      | 2 types: ·         Local 当地币种 ·         USD 美金（标准）                                                                                                                                              | Local: £ 78.19 USD: $100.00                                                                                                                                                                                                                                                                                  | 1 type:[[MZ1]](#_msocom_1) [[MX2]](#_msocom_2) [[MZ3]](#_msocom_3) [[EL4]](#_msocom_4) [[MX5]](#_msocom_5) [[MX6]](#_msocom_6) [[MX7]](#_msocom_7)  ·         RMB 人民币   Depending on the exchange rate** at which the transaction occurs, the results will be calculated and displayed directly. ** The exchange rate is based<br>LION maintained currency data table |
| CX                | Name                    |                      | ·         First Name ·         Last Name                                                                                                                                                        | ·       First Name: 三 / Crystal ·       Last Name: 张 / Zhang                                                                                                                                                                                                                                                 | Last Name + First Name for Chinese First Name + Last Name for English                                                                                                                                                                                                                                                                                                 |
| CX                | Phone                   |                      | 1 per type per country: ·       Type: **Mobile**/ Assistant/ Home/ Office/Other ·       Country: **CN**/US/….                                                                                   | Phones : [ {number: ‘+86138xxx’, mccCountry: ‘CN’, type: ‘Mobile’}, {number: ‘850xxxx’; mccCountry: ‘US’, type: ‘Mobile’}, {number: ‘390xxxx’; mccCountry: ‘US’, type: ‘Assitant’}]                                                                                                                          | Max. 2 phone for display : Type: Mobile > assistant > Home>…（mobile is the first，ascending by first character） Country : CN >France >US>… （CN is the first, ascending by first character）                                                                                                                                                                             |
| CX                | Email                   |                      | 1 per region: ·       Region: **CN**/US/….                                                                                                                                                      | Emails : [ {value: ‘<EMAIL>’, region: ‘CN’} , {value: ‘<EMAIL>’, region: ‘US’} ]                                                                                                                                                                                                                    | Max. 1 email for display                                                                                                                                                                                                                                                                                                                                              |
| CX                | Address                 |                      | 1 per type per subtype per country ·       Type: **Communication**/ Billing /Shipping ·       Subtype: **Home/ Home<br>er** / Work / Work Other / Vacation /… ·       Country : **CN**/<br> US/ | Addresses: [ {value: {‘city’: ‘上海’, ‘country’: ‘CN’, ‘line1’:’太古汇’,’stateProvince’: ‘上海’}, type: ‘Communication’, subtype: ‘Home’, regionId: ‘CN’},   {value: {‘city’: ‘香港特别行政市’, ‘country’: ‘HK’, ‘line1’:’ FLAT C’,’stateProvince’: ‘香港特别行政区 ’}, type: ‘Communication’, subtype: ‘Home’,<br>ionId: ‘US’} ] | Max. 2 Address for display[[EL8]](#_msocom_8) [[MX9]](#_msocom_9)                                                                                                                                                                                                                                                                                                     |

# Quick-Registration

FA can leverage new-to-market clients existing profile from PGN to pre-fill below information on LION iPad to enable smoother registration process.

The pre-fill information will follow as-is China touchpoint registration standard.Please find below data table – last column for specific pre-fill rule. If existing profile from PGN cannot satisfy China standard, then will leave the field to empty and FA can choose to fill-in the field

| **LION iPad UI** | **Mandatory Field & *Validation Rule* (as-is)** | **Data Field**                                       | **Definition**                              | **Source Data Format**                                                               | **Pre-fill Rule**                                                                                                                                                                                                                                                                                                                                        |
| ---------------- | ----------------------------------------------- | ---------------------------------------------------- | ------------------------------------------- | ------------------------------------------------------------------------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 称谓               | N *Single choice*                               | Salutation                                           | Client’s survived civility                  | Mr. / Ms. / PreferNotToSay / Empty                                                   | ·         Pre-fill only in below standard format: Mr. / Ms. / PreferNotToSaympty ·         If not satisfyondition, then not prefill and leave empty.                                                                                                                                                                                                     |
| 姓                | Y *Free text (EN / CN)*                         | Latinized Name – Last Name Written Name – Last Name  | Client’s survived written name – last name  | Zhang[[EL1]](#_msocom_1) [[MX2]](#_msocom_2) [[EL3]](#_msocom_3) [[MX4]](#_msocom_4) | ·         1st priority: if written name (last & first) is in Chinese  use written name (last & first) ·         2nd priority: written name is not in Chinese (last & first) ; latinized name is not empty  use latinized name (last & first) ·         if notisfy above 2 conditions, then both fields (last name and first name) not and leave empty. |
| 名                | Y *Free text* *(EN / CN)*                       | Latinized Name – First Name Written Name –first Name | Client’s survived written name – first name | Sam                                                                                  |                                                                                                                                                                                                                                                                                                                                                          |

| 地区号                    | Y *Single choice*                       | Phone Number – Country Code | Client’s cleansed phone number                       | 01                                                                                                   | l  country code of the phone number should be in the Appendix. 2.14.1.2 <Phone – Country Code List>. l  If the country code does not in the list, then bothntry code and phone number will not be pre-fill as it is considered phone number and will leave FA to fill-in.                                                                                                 |
| ---------------------- | --------------------------------------- | --------------------------- | ---------------------------------------------------- | ---------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 手机号码                   | Y *Free text (no special character)*    | Phone Number                | Client’s cleansed phone number                       | 8912233421111                                                                                        |                                                                                                                                                                                                                                                                                                                                                                           |
| 国籍                     | N *Single choice*                       | Nationality                 | Client’s survived nationality                        | US                                                                                                   | l  ISO 3166 alpha 2 – list of country code ([Country mapping list.xlsx](https://lpl.sharepoint.com/:x:/s/LIONBusinessEngagement/EVakNUSn3mVHiEYnA0zirDYB2yGeLa8RsOSIFpZqXB70Ow?email=echo.lin%40chanel.com&e=Twd0xa)) l  If not in the list, then nationality will not be-filled.                                                                                         |
| 生日                     | N *Single choice* *(14 < age < 120)*    | Birthday                    | Client’s survived birthday                           | 1991/1/1                                                                                             | l  Pre-fill only when 14 < age < 120; l  If not satisfy above condition, then not prefill and leave empty.[[MX5]](#_msocom_5)                                                                                                                                                                                                                                             |
| 电子邮件                   | N *Free text (email format validation)* | Email                       | Client’s survived email                              | <EMAIL>                                                                                        | l  Pre-fill if pass email format validation l  If not satisfy above condition, then not prefill and leave empty.                                                                                                                                                                                                                                                          |
| 目前居住地地区                | Y *Single choice*                       | Country                     | Client’s cleansed country left in the address        | US[[EL6]](#_msocom_6) [[MX7]](#_msocom_7) [[EL8]](#_msocom_8)                                        | l  ISO 3166 alpha 2 – list of country code ([Country mapping list.xlsx](https://lpl.sharepoint.com/:x:/s/LIONBusinessEngagement/EVakNUSn3mVHiEYnA0zirDYB2yGeLa8RsOSIFpZqXB70Ow?email=echo.lin%40chanel.com&e=Twd0xa)) l  If not in the list, then nationality will not be-filled.                                                                                         |
| 所在省                    | N *Single choice*                       | State / Province            | Client’s cleansed state/province left in the address | ~~MA~~ 上海市                                                                                           | l  LOR = CN l  [Province in as-is iPad registraion sincle choice option. Detailed list via mapping list.xlsx](https://lpl.sharepoint.com/:x:/s/LIONBusinessEngagement/EVakNUSn3mVHiEYnA0zirDYB2yGeLa8RsOSIFpZqXB70Ow?email=echo.lin%40chanel.com&e=Twd0xa) - Sheet iPad Validation - Column A – Province l  If not satisfy above condition, then not prefill andve empty. |
| 所在市                    | N *Single choice*                       | City                        | Client’s cleansed city left in the address           | ~~Boston~~[[EL9]](#_msocom_9) [[MX10]](#_msocom_10) [[EL11]](#_msocom_11) [[MX12]](#_msocom_12)  上海市 | l  LOR = CN l  [City in as-is iPad registraion sincle choice option. Detailed list via mapping list.xlsx](https://lpl.sharepoint.com/:x:/s/LIONBusinessEngagement/EVakNUSn3mVHiEYnA0zirDYB2yGeLa8RsOSIFpZqXB70Ow?email=echo.lin%40chanel.com&e=Twd0xa) - Sheet iPad Validation - Column A – City l  If not satisfy above condition, then not prefill andve empty.         |
| 所在区                    | N *Single choice*                       | Addressline1                | Client’s cleansed addressline1                       | ~~1234 district~~ 静安区                                                                                | l  LOR = CN l  [District in as-is iPad registraion sincle choice option. Detailed list via mapping list.xlsx](https://lpl.sharepoint.com/:x:/s/LIONBusinessEngagement/EVakNUSn3mVHiEYnA0zirDYB2yGeLa8RsOSIFpZqXB70Ow?email=echo.lin%40chanel.com&e=Twd0xa) - Sheet iPad Validation - Column A –District l  If not satisfy above condition, then not prefill andve empty.  |
| 详细地址                   | N *Free text*                           | Addressline1                | Client’s cleansed addressline1                       | 1234 district                                                                                        | l  No special characters If not satisfy above condition, then not prefill and leave empty.                                                                                                                                                                                                                                                                                |
| Privacy policy consent | Y *Tick box*                            | N/A                         | N/A                                                  | N/A                                                                                                  | Not pre-fill                                                                                                                                                                                                                                                                                                                                                              |
| CBDT Consent           | N *Tick box*                            | N/A                         | N/A                                                  | N/A                                                                                                  | Not pre-fill                                                                                                                                                                                                                                                                                                                                                              |
| Data Marketing Consent | N *Tick box*                            | N/A                         | N/A                                                  | N/A                                                                                                  | Not pre-fill                                                                                                                                                                                                                                                                                                                                                              |

# Client Search Result data attribute mapping
#### 1.1.1.1   Client basic Info
| **UI Name**              | **Data Attributes**                                                                                    |
| ------------------------ | ------------------------------------------------------------------------------------------------------ |
| Global ID                | Results -> Customer Profile -> customerGlobalI                                                         |
| Name                     | Results -> Customer Profile ->basicInfo -> latinizedName -> automatic -> lastName +dleName + firstName |
| Region                   | Markets - > regionId                                                                                   |
| Nationality              | Results -> Customer Profile -> basicInfo -> nationality                                                |
| Birthday                 | Results -> Customer Profile -> BasicInfo -> birthDate                                                  |
| FSN Global Client Status | Results -> Customer Profile -> BasicInfo -> globalClientStatus -> FSHStatus                            |
| Member Type              | Results -> Customer Profile -> DetailedInfo -> clientAttribute -> employee                             |
| WFJ Global Client Status | Results -> Customer Profile -> BasicInfo -> globalClientStatus -> WFJStatus                            |
| Salutation               | Results -> Customer Profile -> basicInfo -> civility                                                   |

#### 1.1.1.2   Client Tag

| **UI Name**                   | **Data Attributes**                                                     |
| ----------------------------- | ----------------------------------------------------------------------- |
| FSN Global Segment            | ClientMap > globalSegmentFSH > segmentValue                             |
| WFJ Global Segment Aggregated | ClientMap > globalSegmentWFJAggregated > segmentValue                   |
| Employee                      | Results > Customer Profile > DetailedInfo > clientAttribute > employee  |
| FSN Prospect                  | Results > Customer Profile > BasicInfo > globalClientStatus > FSHStatus |
| WFJ High-End Client Flag      | ClientMap > globalClientMapWFJ > highEndClientFlag                      |
| WFJ Prospect                  | Results > Customer Profile > BasicInfo > globalClientStatus > WFJStatus |

#### 1.1.1.3   Client Basic Info

| **UI Name**           | **Data Attributes**                                                                                                             |
| --------------------- | ------------------------------------------------------------------------------------------------------------------------------- |
| Global ID             | Results > Customer Profile > customerGlobalId                                                                                   |
| Name                  | Results > Customer Profile >basicInfo > latinizedName > automatic >tName + middleName + firstName                               |
| Phone 1 / Phone 2     | Results > Customer Profile > contactInfo > phones > number                                                                      |
| Salutation            | Results > Customer Profile > basicInfo > civility                                                                               |
| Age                   | Results > Customer Profile > BasicInfo > birthDate > year                                                                       |
| Birthday              | Results > Customer Profile > basicInfo > birthDate > year + month + day                                                         |
| Region                | Markets - > regionId                                                                                                            |
| Market                | Markets - > marketId                                                                                                            |
| Nationality           | Results > Customer Profile > basicInfo > nationality                                                                            |
| Location of Residence | Results > Customer Profile > basicInfo > customerCountry > countryOfResidence                                                   |
| Address               | Results > Customer Profile > contactInfo > addressesTypeCountry > value >ntry + stateProvince + city + line 1 + line 2 + line 3 |
| Email                 | Results > Customer Profile > contactInfo > emails > value                                                                       |

#### *******   Client Detail Info

| UI Displayed            | Data Attributes                                                                                                                              |
| ----------------------- | -------------------------------------------------------------------------------------------------------------------------------------------- |
|                         |                                                                                                                                              |
| FSN Segment             | ClientMap > globalSegmentFSH > segmentValue                                                                                                  |
| FSN Segment Y-1 Q4      | ClientMap > globalSegmentFSH > segmentValuePreviousPeriod                                                                                    |
| WFJ Segment Aggregated  | ClientMap > globalSegmentWFJAggregated > segmentValue                                                                                        |
| WFJ Segment Detail      | ClientMap > globalSegmentWFJDetailed > segmentValue                                                                                          |
| FSN First Purchase Date | ClientMap > globalClientMapFSH >firstTransactionDateFSH                                                                                      |
| FSN Last Purchase Date  | ClientMap > globalClientMapFSH >lastTransactionDateFSH                                                                                       |
| WFJ First Purchase Date | ClientMap > globalClientMapWFJ >firstTransactionDateWFJ                                                                                      |
| WFJ Last Purchase Date  | ClientMap > globalClientMapWFJ >lastTransactionDateWFJ                                                                                       |
| Data Marketing Consent  | ClientPreferencesUpperLevel > cpcConsents >consentProperties > consentStatussentType='MARKETING_CONSENT'                                     |
| Phone opt-in /opt-out   | ClientPreferencesUpperLevel > channelPreferences > channelPreferencesProperties > optinValueFlagn channelPreferencesType='PHONE'             |
| SMS opt-in/out          | ClientPreferencesUpperLevel > channelPreferences > channelPreferencesProperties > optinValueFlagn channelPreferencesType='SMS'               |
| Email opt-in/opt-out    | ClientPreferencesUpperLevel > channelPreferences > channelPreferencesProperties > optinValueFlagn channelPreferencesType='EMAIL'             |
| DM opt-in/opt-out       | ClientPreferencesUpperLevel > channelPreferences > channelPreferencesProperties > optinValueFlagn channelPreferencesType='MAIL'              |
| IM opt-in/opt-out       | ClientPreferencesUpperLevel > channelPreferences > channelPreferencesProperties > optinValueFlagn channelPreferencesType='INSTANT_MESSAGING' |

#### 1.1.1.7   Client

Transaction Information

| **UI Name**             | **Data Attributes**                                                               | **Data Source** |
| ----------------------- | --------------------------------------------------------------------------------- | --------------- |
| Transaction Date        | Results > Transactions > header > creationTimestamp                               | PGN             |
| Transaction Region      | Results > Transactions > header > originalMarket  > regionId                      | PGN             |
| Transaction Market      | Results > Transactions > header > originalMarket  > marketId                      | PGN             |
| Transaction Boutique    | Results > Transactions > header > boutiqueId                                      | PGN             |
| Remote Sales            | Results > Transactions > header > saleSubtype                                     | PGN             |
| Transaction Quantity    | Results > Transactions > details > quantity                                       | PGN             |
| Transaction Amount      | Transaction > header > totalAmounts[] > convertedAmtValue  amt='netRevenueTax     | PGN             |

Detail Information

| **UI Name**             | **Data Attributes**                                                               | **Data Source** |
| ----------------------- | --------------------------------------------------------------------------------- | --------------- |
| Beijing Time            | Transaction > header > creationTimestamp                                          | PGN             |
| Market Time             | Transaction > header > creationTimestamp                                          | PGN             |
| Region                  | Results > Transactions > header > markets > regionId                              | PGN             |
| Market                  | Results > Transactions > header > markets > marketId                              | PGN             |
| Source System of Record | Transactions > header > systemOfRecordId > sourceSystemId                         | PGN             |
| Invoice Number          | Transaction > header > TransactionHeader >  invoiceNum                            | PGN             |
| Customer ID             | Transaction > header > TransactionHeader > customerGlobalId                       | PGN             |
| DOCNO                   | Results > Transactions > header > receiptNum                                      | PGN             |
| Purchase FA/JA          | Transaction > header > TransactionHeader > globalEmpId > id                       | PGN             |
| Boutique ID             | Transaction > header > TransactionHeader > boutiqueId                             | PGN             |
| Boutique                | locations> locationName                                                           | PGN             |
| Division (Boutique)     | Transaction > header > TransactionHeader > divisionCode                           | PGN             |

Product Information

| **UI Name**             | **Data Attributes**                                                               | **Data Source** |
| ----------------------- | --------------------------------------------------------------------------------- | --------------- |
| Product Name            | Transaction > details > productInfos > itemDescription                            | PGN / PEARL     |
| Remote Sales            | Results > Transactions > header > saleSubtype                                     | PGN             |
| Markdown Flag           | Results > Transactions > details > amounts > markdown > markdownAmounts           | PGN             |
| Product Code            | Transaction > details > productInfos > productCode > code                         | PGN             |
| Product Serial Number   | Transaction > details > productInfos > serialNumbers                              | PGN             |
| Purchase Type           | Transaction > details > productInfos > productType                                | PGN             |
| Product Amount          | Transaction > header > totalAmounts[] > convertedAmtValue amtName='netRevenueTax' | PGN             |
| Product Quantity        | Transactions > Details > Quantity                                                 | PGN             |
